<UserControl x:Class="ProjectDigitizer.Studio.Controls.Functions.FunctionPropertyPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="800"
             d:DesignWidth="600"
             Background="{DynamicResource MaterialDesignPaper}">

        <UserControl.Resources>
                <!-- 区域标题样式 -->
                <Style x:Key="SectionHeaderStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="FontWeight"
                                Value="SemiBold"/>
                        <Setter Property="Foreground"
                                Value="#2196F3"/>
                        <Setter Property="Margin"
                                Value="0,0,0,8"/>
                </Style>

                <!-- 卡片样式 -->
                <Style x:Key="CardStyle"
                       TargetType="Border">
                        <Setter Property="Background"
                                Value="{DynamicResource MaterialDesignCardBackground}"/>
                        <Setter Property="CornerRadius"
                                Value="4"/>
                        <Setter Property="Padding"
                                Value="16"/>
                        <Setter Property="Margin"
                                Value="4"/>
                        <Setter Property="Effect">
                                <Setter.Value>
                                        <DropShadowEffect ShadowDepth="1"
                                                          Direction="270"
                                                          Color="Black"
                                                          Opacity="0.2"
                                                          BlurRadius="5"/>
                                </Setter.Value>
                        </Setter>
                </Style>

                <!-- 分组标题样式 -->
                <Style x:Key="GroupHeaderStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="FontWeight"
                                Value="Medium"/>
                        <Setter Property="Foreground"
                                Value="{DynamicResource MaterialDesignBody}"/>
                        <Setter Property="Margin"
                                Value="0,0,0,12"/>
                </Style>

                <!-- 图标按钮样式 -->
                <Style x:Key="IconButtonStyle"
                       TargetType="Button">
                        <Setter Property="Width"
                                Value="32"/>
                        <Setter Property="Height"
                                Value="32"/>
                        <Setter Property="Margin"
                                Value="4,0"/>
                        <Setter Property="Background"
                                Value="#E3F2FD"/>
                        <Setter Property="BorderBrush"
                                Value="#2196F3"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                </Style>

                <!-- 字段项样式 -->
                <Style x:Key="FieldItemStyle"
                       TargetType="Border">
                        <Setter Property="Background"
                                Value="{DynamicResource MaterialDesignChipBackground}"/>
                        <Setter Property="CornerRadius"
                                Value="12"/>
                        <Setter Property="Padding"
                                Value="8,4"/>
                        <Setter Property="Margin"
                                Value="2"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="Transparent"/>
                        <Style.Triggers>
                                <!-- 可用状态 -->
                                <DataTrigger Binding="{Binding State}"
                                             Value="Available">
                                        <Setter Property="BorderBrush"
                                                Value="{DynamicResource MaterialDesignDivider}"/>
                                        <Setter Property="BorderThickness"
                                                Value="1"/>
                                </DataTrigger>
                                <!-- 已连接状态 -->
                                <DataTrigger Binding="{Binding State}"
                                             Value="Connected">
                                        <Setter Property="BorderBrush"
                                                Value="{DynamicResource PrimaryHueMidBrush}"/>
                                        <Setter Property="BorderThickness"
                                                Value="2"/>
                                </DataTrigger>
                                <!-- 已引用状态 -->
                                <DataTrigger Binding="{Binding State}"
                                             Value="Referenced">
                                        <Setter Property="BorderBrush"
                                                Value="{DynamicResource SecondaryHueMidBrush}"/>
                                        <Setter Property="BorderThickness"
                                                Value="2"/>
                                        <Setter Property="Background"
                                                Value="{DynamicResource SecondaryHueLightBrush}"/>
                                </DataTrigger>
                                <!-- 激活状态 -->
                                <DataTrigger Binding="{Binding State}"
                                             Value="Active">
                                        <Setter Property="BorderBrush"
                                                Value="{DynamicResource PrimaryHueMidBrush}"/>
                                        <Setter Property="BorderThickness"
                                                Value="2"/>
                                        <Setter Property="Background"
                                                Value="{DynamicResource PrimaryHueLightBrush}"/>
                                </DataTrigger>
                        </Style.Triggers>
                </Style>

                <!-- GridSplitter样式 -->
                <Style x:Key="GridSplitterStyle"
                       TargetType="GridSplitter">
                        <Setter Property="Background"
                                Value="{DynamicResource MaterialDesignDivider}"/>
                        <Setter Property="HorizontalAlignment"
                                Value="Stretch"/>
                        <Setter Property="VerticalAlignment"
                                Value="Stretch"/>
                </Style>

                <!-- 自定义Expander样式 -->
                <Style x:Key="MaterialExpanderStyle"
                       TargetType="Expander">
                        <Setter Property="Background"
                                Value="{DynamicResource MaterialDesignCardBackground}"/>
                        <Setter Property="BorderBrush"
                                Value="{DynamicResource MaterialDesignDivider}"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="Foreground"
                                Value="{DynamicResource MaterialDesignBody}"/>
                        <Setter Property="FontWeight"
                                Value="Medium"/>
                        <Setter Property="Padding"
                                Value="8"/>
                </Style>
        </UserControl.Resources>

        <Grid>
                <Grid.RowDefinitions>
                        <RowDefinition Height="2*"
                                       MinHeight="200"/>
                        <!-- 数据来源区 -->
                        <RowDefinition Height="1*"
                                       MinHeight="120"/>
                        <!-- 数据选择池 -->
                        <RowDefinition Height="2*"
                                       MinHeight="200"/>
                        <!-- 函数表达式编辑器 -->
                        <RowDefinition Height="1.5*"
                                       MinHeight="150"/>
                        <!-- 数据预览/结果区 -->
                </Grid.RowDefinitions>

                <!-- 数据来源区 -->
                <Expander Grid.Row="0"
                          Header="数据来源"
                          IsExpanded="True"
                          Margin="4"
                          Style="{StaticResource MaterialExpanderStyle}">
                        <Border Style="{StaticResource CardStyle}"
                                Margin="0">
                                <DockPanel>
                                        <TextBlock DockPanel.Dock="Top"
                                                   Text="数据来源"
                                                   Style="{StaticResource SectionHeaderStyle}"/>
                                        <StackPanel DockPanel.Dock="Top"
                                                    Margin="0,0,0,12">
                                                <!-- 数据来源模式切换 -->
                                                <ToggleButton x:Name="DataSourceModeToggle"
                                                              Content="节点数据"
                                                              ToolTip="切换数据来源：节点数据 / 外部文件"
                                                              Margin="0,0,0,8"
                                                              Background="#E3F2FD"
                                                              BorderBrush="#2196F3"
                                                              BorderThickness="1"
                                                              Padding="8,4"
                                                              HorizontalAlignment="Left">
                                                        <ToggleButton.Style>
                                                                <Style TargetType="ToggleButton">
                                                                        <Setter Property="Template">
                                                                                <Setter.Value>
                                                                                        <ControlTemplate TargetType="ToggleButton">
                                                                                                <Border Background="{TemplateBinding Background}"
                                                                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                                                                        CornerRadius="4"
                                                                                                        Padding="{TemplateBinding Padding}">
                                                                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                                                                          VerticalAlignment="Center"/>
                                                                                                </Border>
                                                                                                <ControlTemplate.Triggers>
                                                                                                        <Trigger Property="IsChecked"
                                                                                                                 Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#2196F3"/>
                                                                                                                <Setter Property="Foreground"
                                                                                                                        Value="White"/>
                                                                                                        </Trigger>
                                                                                                        <Trigger Property="IsMouseOver"
                                                                                                                 Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#BBDEFB"/>
                                                                                                        </Trigger>
                                                                                                </ControlTemplate.Triggers>
                                                                                        </ControlTemplate>
                                                                                </Setter.Value>
                                                                        </Setter>
                                                                </Style>
                                                        </ToggleButton.Style>
                                                </ToggleButton>

                                                <!-- 搜索框 -->
                                                <TextBox x:Name="FieldSearchBox"
                                                         materialDesign:HintAssist.Hint="搜索字段..."
                                                         FontSize="12"
                                                         Height="36"/>
                                        </StackPanel>

                                        <!-- 节点选择列表 -->
                                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                      HorizontalScrollBarVisibility="Disabled"
                                                      MaxHeight="200">
                                                <StackPanel>
                                                        <!-- 可连接节点列表 -->
                                                        <ItemsControl x:Name="AvailableNodesControl"
                                                                      ItemsSource="{Binding AvailableSourceNodes}">
                                                                <ItemsControl.ItemTemplate>
                                                                        <DataTemplate>
                                                                                <Border Background="White"
                                                                                        BorderBrush="#E0E0E0"
                                                                                        BorderThickness="1"
                                                                                        CornerRadius="4"
                                                                                        Margin="0,0,0,4"
                                                                                        Padding="8,6">
                                                                                        <Grid>
                                                                                                <Grid.ColumnDefinitions>
                                                                                                        <ColumnDefinition Width="*"/>
                                                                                                        <ColumnDefinition Width="Auto"/>
                                                                                                        <ColumnDefinition Width="Auto"/>
                                                                                                </Grid.ColumnDefinitions>

                                                                                                <!-- 节点信息 -->
                                                                                                <StackPanel Grid.Column="0"
                                                                                                            Orientation="Vertical">
                                                                                                        <TextBlock Text="{Binding NodeTitle}"
                                                                                                                   FontWeight="Medium"
                                                                                                                   FontSize="12"
                                                                                                                   Foreground="#333"/>
                                                                                                        <TextBlock Text="{Binding NodeType}"
                                                                                                                   FontSize="10"
                                                                                                                   Foreground="#666"
                                                                                                                   Margin="0,2,0,0"/>
                                                                                                </StackPanel>

                                                                                                <!-- 连接状态指示器 -->
                                                                                                <Ellipse Grid.Column="1"
                                                                                                         Width="10"
                                                                                                         Height="10"
                                                                                                         Margin="8,0,8,0"
                                                                                                         VerticalAlignment="Center">
                                                                                                        <Ellipse.Style>
                                                                                                                <Style TargetType="Ellipse">
                                                                                                                        <Setter Property="Fill"
                                                                                                                                Value="#E0E0E0"/>
                                                                                                                        <Style.Triggers>
                                                                                                                                <DataTrigger Binding="{Binding IsConnected}"
                                                                                                                                             Value="True">
                                                                                                                                        <Setter Property="Fill"
                                                                                                                                                Value="#4CAF50"/>
                                                                                                                                </DataTrigger>
                                                                                                                        </Style.Triggers>
                                                                                                                </Style>
                                                                                                        </Ellipse.Style>
                                                                                                </Ellipse>

                                                                                                <!-- 连接按钮 -->
                                                                                                <Button Grid.Column="2"
                                                                                                        FontSize="10"
                                                                                                        Padding="8,2"
                                                                                                        Foreground="White"
                                                                                                        BorderThickness="0"
                                                                                                        Click="ConnectNodeButton_Click"
                                                                                                        Tag="{Binding}">
                                                                                                        <Button.Style>
                                                                                                                <Style TargetType="Button">
                                                                                                                        <!-- 默认样式 -->
                                                                                                                        <Setter Property="Content"
                                                                                                                                Value="连接"/>
                                                                                                                        <Setter Property="Background"
                                                                                                                                Value="#2196F3"/>
                                                                                                                        <Style.Triggers>
                                                                                                                                <DataTrigger Binding="{Binding IsConnected}"
                                                                                                                                             Value="True">
                                                                                                                                        <Setter Property="Content"
                                                                                                                                                Value="断开"/>
                                                                                                                                        <Setter Property="Background"
                                                                                                                                                Value="#F44336"/>
                                                                                                                                </DataTrigger>
                                                                                                                        </Style.Triggers>
                                                                                                                </Style>
                                                                                                        </Button.Style>
                                                                                                </Button>
                                                                                        </Grid>
                                                                                </Border>
                                                                        </DataTemplate>
                                                                </ItemsControl.ItemTemplate>
                                                        </ItemsControl>

                                                        <!-- 空状态提示 -->
                                                        <TextBlock Text="暂无可连接的节点"
                                                                   FontSize="12"
                                                                   Foreground="#999"
                                                                   FontStyle="Italic"
                                                                   HorizontalAlignment="Center"
                                                                   Margin="0,20,0,0">
                                                                <TextBlock.Style>
                                                                        <Style TargetType="TextBlock">
                                                                                <Setter Property="Visibility"
                                                                                        Value="Collapsed"/>
                                                                                <Style.Triggers>
                                                                                        <DataTrigger Binding="{Binding AvailableSourceNodes.Count}"
                                                                                                     Value="0">
                                                                                                <Setter Property="Visibility"
                                                                                                        Value="Visible"/>
                                                                                        </DataTrigger>
                                                                                </Style.Triggers>
                                                                        </Style>
                                                                </TextBlock.Style>
                                                        </TextBlock>
                                                </StackPanel>
                                        </ScrollViewer>
                                </DockPanel>
                        </Border>
                </Expander>

                <!-- 数据选择池 -->
                <Expander Grid.Row="1"
                          Header="数据选择池"
                          IsExpanded="True"
                          Margin="4"
                          Style="{StaticResource MaterialExpanderStyle}">
                        <Border Style="{StaticResource CardStyle}"
                                Margin="0">
                                <DockPanel>
                                        <TextBlock DockPanel.Dock="Top"
                                                   Text="数据选择池"
                                                   Style="{StaticResource SectionHeaderStyle}"/>
                                        <StackPanel DockPanel.Dock="Top"
                                                    Orientation="Horizontal"
                                                    Margin="0,0,0,8">
                                                <Button x:Name="ClearSelectedFieldsButton"
                                                        Style="{StaticResource IconButtonStyle}"
                                                        Content="🗑️"
                                                        ToolTip="清空已选字段"/>
                                        </StackPanel>
                                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                                                <ItemsControl x:Name="SelectedFieldsPanel"
                                                              ItemsSource="{Binding SelectedFields}">
                                                        <ItemsControl.ItemsPanel>
                                                                <ItemsPanelTemplate>
                                                                        <WrapPanel Orientation="Horizontal"/>
                                                                </ItemsPanelTemplate>
                                                        </ItemsControl.ItemsPanel>
                                                        <ItemsControl.ItemTemplate>
                                                                <DataTemplate>
                                                                        <Border Background="#E3F2FD"
                                                                                BorderBrush="#2196F3"
                                                                                BorderThickness="1"
                                                                                CornerRadius="12"
                                                                                Padding="8,4"
                                                                                Margin="2">
                                                                                <StackPanel Orientation="Horizontal">
                                                                                        <TextBlock Text="{Binding Name}"
                                                                                                   FontSize="11"
                                                                                                   VerticalAlignment="Center"/>
                                                                                        <Button Width="16"
                                                                                                Height="16"
                                                                                                Padding="0"
                                                                                                Margin="4,0,0,0"
                                                                                                Content="×"
                                                                                                ToolTip="移除字段"
                                                                                                Background="Transparent"
                                                                                                BorderThickness="0"
                                                                                                FontSize="12"
                                                                                                FontWeight="Bold"/>
                                                                                </StackPanel>
                                                                        </Border>
                                                                </DataTemplate>
                                                        </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                        </ScrollViewer>
                                </DockPanel>
                        </Border>
                </Expander>

                <!-- 函数表达式编辑器 -->
                <Expander Grid.Row="2"
                          Header="函数表达式编辑器"
                          IsExpanded="True"
                          Margin="4"
                          Style="{StaticResource MaterialExpanderStyle}">
                        <Border Style="{StaticResource CardStyle}"
                                Margin="0">
                                <DockPanel>
                                        <TextBlock DockPanel.Dock="Top"
                                                   Text="函数表达式编辑器"
                                                   Style="{StaticResource SectionHeaderStyle}"/>

                                        <!-- 函数操作按钮 -->
                                        <StackPanel DockPanel.Dock="Top"
                                                    Orientation="Horizontal"
                                                    Margin="0,0,0,8">
                                                <Button x:Name="AddFunctionButton"
                                                        Style="{StaticResource IconButtonStyle}"
                                                        Content="+"
                                                        ToolTip="添加函数表达式"/>
                                                <Button x:Name="ImportTemplateButton"
                                                        Style="{StaticResource IconButtonStyle}"
                                                        Content="📋"
                                                        ToolTip="使用公式模板"
                                                        Click="ImportTemplateButton_Click"/>
                                                <Button x:Name="ExecuteAllButton"
                                                        Style="{StaticResource IconButtonStyle}"
                                                        Content="▶"
                                                        ToolTip="执行所有函数"/>
                                                <Button x:Name="ExportResultsButton"
                                                        Style="{StaticResource IconButtonStyle}"
                                                        Content="📤"
                                                        ToolTip="导出结果"/>
                                        </StackPanel>

                                        <!-- 函数列表 -->
                                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                      MaxHeight="300">
                                                <ItemsControl x:Name="FunctionListControl"
                                                              ItemsSource="{Binding Functions}">
                                                        <ItemsControl.ItemTemplate>
                                                                <DataTemplate>
                                                                        <Border Background="White"
                                                                                BorderBrush="#E0E0E0"
                                                                                BorderThickness="1"
                                                                                CornerRadius="4"
                                                                                Margin="0,0,0,8"
                                                                                Padding="8">
                                                                                <Grid>
                                                                                        <Grid.RowDefinitions>
                                                                                                <RowDefinition Height="Auto"/>
                                                                                                <RowDefinition Height="Auto"/>
                                                                                        </Grid.RowDefinitions>
                                                                                        <Grid.ColumnDefinitions>
                                                                                                <ColumnDefinition Width="*"/>
                                                                                                <ColumnDefinition Width="Auto"/>
                                                                                        </Grid.ColumnDefinitions>

                                                                                        <!-- 函数名称输入 -->
                                                                                        <TextBox Grid.Row="0"
                                                                                                 Grid.Column="0"
                                                                                                 Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                                                                                 FontWeight="Medium"
                                                                                                 FontSize="12"
                                                                                                 Margin="0,0,8,4"
                                                                                                 BorderBrush="#CCCCCC"
                                                                                                 BorderThickness="1"
                                                                                                 Padding="4,2"/>

                                                                                        <!-- 删除按钮 -->
                                                                                        <Button Grid.Row="0"
                                                                                                Grid.Column="1"
                                                                                                Width="20"
                                                                                                Height="20"
                                                                                                Content="×"
                                                                                                ToolTip="删除函数"
                                                                                                Background="#FFEBEE"
                                                                                                BorderBrush="#F44336"
                                                                                                BorderThickness="1"
                                                                                                FontSize="12"
                                                                                                FontWeight="Bold"
                                                                                                Foreground="#F44336"
                                                                                                Click="DeleteFunctionButton_Click"
                                                                                                Tag="{Binding}"/>

                                                                                        <!-- 表达式输入 -->
                                                                                        <TextBox Grid.Row="1"
                                                                                                 Grid.Column="0"
                                                                                                 Grid.ColumnSpan="2"
                                                                                                 Text="{Binding Expression, UpdateSourceTrigger=PropertyChanged}"
                                                                                                 FontFamily="Consolas"
                                                                                                 FontSize="11"
                                                                                                 Margin="0,0,0,0"
                                                                                                 BorderBrush="#CCCCCC"
                                                                                                 BorderThickness="1"
                                                                                                 Padding="4,2"
                                                                                                 materialDesign:HintAssist.Hint="输入函数表达式..."/>
                                                                                </Grid>
                                                                        </Border>
                                                                </DataTemplate>
                                                        </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                        </ScrollViewer>
                                </DockPanel>
                        </Border>
                </Expander>

                <!-- 数据预览与结果 -->
                <Expander Grid.Row="3"
                          Header="数据预览与结果"
                          IsExpanded="True"
                          Margin="4"
                          Style="{StaticResource MaterialExpanderStyle}">
                        <Border Style="{StaticResource CardStyle}"
                                Margin="0">
                                <DockPanel>
                                        <TextBlock DockPanel.Dock="Top"
                                                   Text="数据预览与结果"
                                                   Style="{StaticResource SectionHeaderStyle}"/>

                                        <!-- 切换标签 -->
                                        <TabControl>
                                                <TabItem Header="数据预览">
                                                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                                                                <DataGrid x:Name="DataPreviewGrid"
                                                                          AutoGenerateColumns="True"
                                                                          IsReadOnly="True"
                                                                          CanUserAddRows="False"
                                                                          CanUserDeleteRows="False"
                                                                          BorderBrush="#CCCCCC"
                                                                          BorderThickness="1"
                                                                          MaxHeight="200"/>
                                                        </ScrollViewer>
                                                </TabItem>
                                                <TabItem Header="计算结果">
                                                        <DataGrid x:Name="ResultDataGrid"
                                                                  ItemsSource="{Binding FunctionResults}"
                                                                  AutoGenerateColumns="False"
                                                                  IsReadOnly="True"
                                                                  CanUserAddRows="False"
                                                                  CanUserDeleteRows="False"
                                                                  BorderBrush="#CCCCCC"
                                                                  BorderThickness="1">
                                                                <DataGrid.Columns>
                                                                        <DataGridTextColumn Header="函数名称"
                                                                                            Binding="{Binding Name}"
                                                                                            Width="120"/>
                                                                        <DataGridTextColumn Header="表达式"
                                                                                            Binding="{Binding Expression}"
                                                                                            Width="*"/>
                                                                        <DataGridTextColumn Header="结果"
                                                                                            Binding="{Binding Result}"
                                                                                            Width="100"/>
                                                                        <DataGridTextColumn Header="状态"
                                                                                            Binding="{Binding Status}"
                                                                                            Width="80"/>
                                                                </DataGrid.Columns>
                                                        </DataGrid>
                                                </TabItem>
                                        </TabControl>
                                </DockPanel>
                        </Border>
                </Expander>
        </Grid>
</UserControl>
